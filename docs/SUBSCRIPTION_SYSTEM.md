# BuddyChip Subscription System

## Overview

BuddyChip now features a comprehensive 4-tier subscription system designed to provide clear value progression from free users to enterprise teams.

## Subscription Tiers

### 🆓 Free Tier - $0/month
**Target**: New users trying the platform
- 50 AI calls/month
- 5 image generations/month
- 1 monitored account
- 100 mentions/month
- 25 mentions per sync
- 50 max total mentions
- 1GB storage
- 1 team member
- 10 Cookie API calls/month
- Community support

### ⚡ Reply Guy - $9/month
**Target**: Individual users getting started
- 1,000 AI calls/month (20x more than free)
- 20 image generations/month
- 3 monitored accounts
- 1,000 mentions/month
- 25 mentions per sync
- 100 max total mentions
- 1GB storage
- 1 team member
- 50 Cookie API calls/month
- Email support

### 👑 Reply God - $29/month
**Target**: Power users and small teams
- 5,000 AI calls/month
- 100 image generations/month
- 10 monitored accounts
- 5,000 mentions/month
- 100 mentions per sync
- 500 max total mentions
- 5GB storage
- 3 team members
- 200 Cookie API calls/month
- Priority support
- Custom personas

### 👥 Team - $99/month
**Target**: Teams and organizations
- Unlimited AI calls
- Unlimited image generations
- 50 monitored accounts
- Unlimited mentions/month
- 200 mentions per sync
- 2,000 max total mentions
- 20GB storage
- 10 team members
- Unlimited Cookie API calls
- Dedicated support
- Team collaboration
- Admin dashboard
- Custom integrations

## Database Schema

### Subscription Plans Table
```sql
subscription_plans (
  id: string (primary key)
  name: string (unique) -- 'free', 'reply-guy', 'reply-god', 'team'
  displayName: string -- 'Free', 'Reply Guy', 'Reply God', 'Team'
  description: string
  price: decimal(10,2)
  baseUsers: int
  additionalUserPrice: decimal(10,2) -- for team plan scaling
  isActive: boolean
  createdAt: datetime
  updatedAt: datetime
)
```

### Plan Features Table
```sql
plan_features (
  id: string (primary key)
  planId: string (foreign key)
  feature: FeatureType (enum)
  limit: int -- -1 for unlimited
  createdAt: datetime
  updatedAt: datetime
)
```

### Feature Types
- `AI_CALLS` - AI response generations
- `IMAGE_GENERATIONS` - AI image creations
- `MONITORED_ACCOUNTS` - Twitter accounts to monitor
- `MENTIONS_PER_MONTH` - Monthly mention processing limit
- `MENTIONS_PER_SYNC` - Mentions fetched per sync operation
- `MAX_TOTAL_MENTIONS` - Maximum unreplied mentions per account
- `STORAGE_GB` - File storage limit
- `TEAM_MEMBERS` - Team size limit
- `COOKIE_API_CALLS` - Crypto data API calls

## Key Components

### 1. User Service (`apps/web/src/lib/user-service.ts`)
- **Default Plan**: New users start with 'free' tier
- **Feature Checking**: `canUserUseFeature()` and `canUserUseClerkFeature()`
- **Plan Limits**: Configurable limits for both legacy and Clerk billing
- **JIT User Creation**: Automatic user creation with free plan

### 2. Billing Router (`apps/web/src/routers/billing.ts`)
- **Plan Management**: Get available plans, current subscription
- **Usage Tracking**: Real-time feature usage monitoring
- **Feature Gates**: Check if user can use specific features
- **Upgrade Flow**: Plan change functionality

### 3. UI Components

#### UpgradePrompt (`apps/web/src/components/upgrade-prompt.tsx`)
- Smart upgrade suggestions based on current plan
- Usage-based prompts when approaching limits
- Clear value proposition for next tier
- One-click upgrade functionality

#### FeatureGate (`apps/web/src/components/feature-gate.tsx`)
- Blocks access when feature limits reached
- Shows upgrade options with pricing
- Graceful fallback for locked features
- Integration with billing system

#### FreeTierOnboarding (`apps/web/src/components/free-tier-onboarding.tsx`)
- Welcome experience for new free users
- Feature overview and usage tracking
- Upgrade teasers and value proposition
- Getting started guidance

### 4. Pricing Configuration (`apps/web/src/lib/subscriptions.ts`)
- Centralized plan definitions
- Model access configuration
- Rate limiting settings
- Feature descriptions

## Implementation Details

### Feature Progression Logic
Each tier provides meaningful upgrades:
- **Free → Reply Guy**: 20x AI calls, 3x accounts
- **Reply Guy → Reply God**: 5x AI calls, team features
- **Reply God → Team**: Unlimited usage, enterprise features

### Rate Limiting
- Per-user, per-feature tracking
- Monthly billing cycle resets
- Graceful degradation when limits reached
- Real-time usage monitoring

### Upgrade Flow
1. User hits feature limit
2. FeatureGate component blocks access
3. UpgradePrompt shows next tier benefits
4. One-click upgrade via billing router
5. Immediate access to new features

### Free Tier Strategy
- Generous enough to provide value
- Clear upgrade path when users grow
- Onboarding to demonstrate platform value
- Community support to reduce costs

## Testing

### Subscription Test (`test/subscription-test.ts`)
Comprehensive test suite covering:
- Plan structure validation
- Feature coverage verification
- Pricing progression checks
- Limit progression validation
- Database integrity

### Manual Testing Checklist
- [ ] Free user signup and onboarding
- [ ] Feature limit enforcement
- [ ] Upgrade prompts display correctly
- [ ] Plan changes work smoothly
- [ ] Usage tracking accuracy
- [ ] Billing integration

## Migration Notes

### Database Changes Made
1. Removed duplicate team plan ($299)
2. Added free tier with complete feature set
3. Updated team plan to unified structure ($99)
4. Standardized all plan features (9 features per plan)
5. Updated pricing to match code configuration

### Code Changes Made
1. Updated default plan to 'free' in user service
2. Added free tier to all plan configurations
3. Updated billing router with new features
4. Created comprehensive UI components
5. Updated pricing page with 4-tier structure

## Future Enhancements

### Potential Additions
- Annual billing discounts
- Usage-based pricing for enterprise
- Add-on features (extra storage, accounts)
- Team member management interface
- Advanced analytics for team plans
- Custom plan creation for enterprise

### Monitoring & Analytics
- Conversion rates between tiers
- Feature usage patterns
- Churn analysis by plan
- Revenue per user tracking
- Support ticket volume by tier

## Support & Documentation

### User-Facing Documentation
- Plan comparison page updated
- Feature limits clearly displayed
- Upgrade benefits highlighted
- Billing FAQ updated

### Internal Documentation
- Database schema documented
- API endpoints documented
- Component usage examples
- Testing procedures defined
