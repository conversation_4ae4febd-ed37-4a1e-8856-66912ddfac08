import { PricingTable } from '@clerk/nextjs'
import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'

export default async function PricingPage() {
  const { userId } = await auth()

  // Redirect authenticated users to dashboard if they already have access
  if (userId) {
    // You can add logic here to check if user already has a subscription
    // and redirect accordingly
  }

  return (
    <div className="min-h-screen bg-app-background">
      {/* Header */}
      <div className="bg-app-card border-b border-app-stroke">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-app-headline mb-4">
              Choose Your Plan
            </h1>
            <p className="text-xl text-app-sub-headline max-w-2xl mx-auto">
              Unlock the power of AI-driven social media monitoring and response generation
            </p>
          </div>
        </div>
      </div>

      {/* Pricing Table */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-app-card rounded-lg border border-app-stroke shadow-lg p-8">
          <PricingTable 
            appearance={{
              elements: {
                // Customize the appearance to match your app's theme
                card: 'bg-app-card border-app-stroke',
                cardHeader: 'text-app-headline',
                cardContent: 'text-app-sub-headline',
                button: 'bg-app-main hover:bg-app-highlight text-app-secondary',
                price: 'text-app-headline font-bold',
                feature: 'text-app-sub-headline',
              },
            }}
          />
        </div>
      </div>

      {/* Features Comparison */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-app-headline mb-4">
            Compare Features
          </h2>
          <p className="text-lg text-app-sub-headline">
            All plans include our core AI monitoring and response features
          </p>
        </div>

        <div className="bg-app-card rounded-lg border border-app-stroke shadow-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-app-background border-b border-app-stroke">
                <tr>
                  <th className="px-6 py-4 text-left text-app-headline font-semibold">Feature</th>
                  <th className="px-6 py-4 text-center text-app-headline font-semibold">Free</th>
                  <th className="px-6 py-4 text-center text-app-headline font-semibold">Reply Guy</th>
                  <th className="px-6 py-4 text-center text-app-headline font-semibold">Reply God</th>
                  <th className="px-6 py-4 text-center text-app-headline font-semibold">Team</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-app-stroke">
                <tr>
                  <td className="px-6 py-4 text-app-headline">Price</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline font-semibold text-green-600">$0/month</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline font-semibold">$9/month</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline font-semibold">$29/month</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline font-semibold">$99/month</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-app-headline">AI Calls per Month</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">50</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">1,000</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">5,000</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">Unlimited</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-app-headline">Image Generations</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">5</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">20</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">100</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">Unlimited</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-app-headline">Monitored Accounts</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">1</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">3</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">10</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">50</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-app-headline">AI Model Access</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">Gemini 2.5 Flash</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">Gemini 2.5 Flash</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">Gemini 2.5 Pro</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">OpenAI o3</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-app-headline">Team Members</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">1</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">1</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">3</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">10</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-app-headline">Support</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">Community</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">Email</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">Priority</td>
                  <td className="px-6 py-4 text-center text-app-sub-headline">Dedicated</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-app-headline mb-4">
            Frequently Asked Questions
          </h2>
        </div>

        <div className="space-y-8">
          <div className="bg-app-card rounded-lg border border-app-stroke p-6">
            <h3 className="text-lg font-semibold text-app-headline mb-2">
              Can I change my plan anytime?
            </h3>
            <p className="text-app-sub-headline">
              Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, 
              and we'll prorate any billing adjustments.
            </p>
          </div>

          <div className="bg-app-card rounded-lg border border-app-stroke p-6">
            <h3 className="text-lg font-semibold text-app-headline mb-2">
              What happens if I exceed my limits?
            </h3>
            <p className="text-app-sub-headline">
              When you reach your plan limits, you'll be notified and can either upgrade your plan 
              or wait for the next billing cycle to reset your usage.
            </p>
          </div>

          <div className="bg-app-card rounded-lg border border-app-stroke p-6">
            <h3 className="text-lg font-semibold text-app-headline mb-2">
              Is there a free trial?
            </h3>
            <p className="text-app-sub-headline">
              All new users start with the Reply Guy plan features for the first 7 days to explore 
              the platform before committing to a subscription.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
