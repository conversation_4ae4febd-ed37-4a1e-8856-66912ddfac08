/**
 * Billing Router for BuddyChip
 * 
 * Handles Clerk billing integration and subscription management
 */

import { z } from 'zod';
import { createTRPCRouter, protectedProcedure } from '../lib/trpc';
import { TRPCError } from '@trpc/server';
import { prisma } from '../lib/db-utils';
import { canUserUseClerkFeature } from '../lib/user-service';
import { auth } from '@clerk/nextjs/server';

export const billingRouter = createTRPCRouter({
  /**
   * Get current user's subscription status
   */
  getSubscription: protectedProcedure
    .query(async ({ ctx }) => {
      console.log('🔍 BillingRouter: Getting subscription for user:', ctx.userId);

      try {
        const user = await prisma.user.findUnique({
          where: { id: ctx.userId },
          select: {
            // @ts-ignore - These fields exist in the database but not yet in generated types
            clerkPlanId: true,
            clerkPlanName: true,
            subscriptionStatus: true,
            subscriptionUpdatedAt: true,
            plan: {
              select: {
                id: true,
                name: true,
                displayName: true,
                description: true,
                price: true,
                features: true,
              },
            },
          },
        }) as any;

        if (!user) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found',
          });
        }

        return {
          clerkPlan: {
            id: user.clerkPlanId,
            name: user.clerkPlanName,
            status: user.subscriptionStatus,
            updatedAt: user.subscriptionUpdatedAt,
          },
          legacyPlan: user.plan,
          isClerkBilling: !!user.clerkPlanName && user.subscriptionStatus === 'active',
        };
      } catch (error) {
        console.error('Error getting subscription:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get subscription information',
        });
      }
    }),

  /**
   * Get usage information for current billing period
   */
  getUsage: protectedProcedure
    .query(async ({ ctx }) => {
      console.log('🔍 BillingRouter: Getting usage for user:', ctx.userId);

      try {
        const features = ['AI_CALLS', 'IMAGE_GENERATIONS', 'MONITORED_ACCOUNTS', 'MENTIONS_PER_MONTH', 'STORAGE_GB'];
        const usageData = [];

        for (const feature of features) {
          const usage = await canUserUseClerkFeature(ctx.userId, feature);
          usageData.push({
            feature,
            currentUsage: usage.currentUsage,
            limit: usage.limit,
            allowed: usage.allowed,
            resetDate: usage.resetDate,
          });
        }

        return usageData;
      } catch (error) {
        console.error('Error getting usage:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get usage information',
        });
      }
    }),

  /**
   * Check if user can use a specific feature
   */
  canUseFeature: protectedProcedure
    .input(z.object({
      feature: z.enum(['AI_CALLS', 'IMAGE_GENERATIONS', 'MONITORED_ACCOUNTS', 'MENTIONS_PER_MONTH', 'STORAGE_GB', 'TEAM_MEMBERS']),
    }))
    .query(async ({ ctx, input }) => {
      console.log('🔍 BillingRouter: Checking feature usage for user:', ctx.userId, 'feature:', input.feature);

      try {
        return await canUserUseClerkFeature(ctx.userId, input.feature);
      } catch (error) {
        console.error('Error checking feature usage:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to check feature availability',
        });
      }
    }),

  /**
   * Get available plans for upgrade/downgrade
   */
  getAvailablePlans: protectedProcedure
    .query(async () => {
      console.log('🔍 BillingRouter: Getting available plans');

      try {
        // Return Clerk billing plan information
        // This would typically come from Clerk's API, but for now we'll return static data
        return [
          {
            id: 'free',
            name: 'Free',
            price: 0,
            currency: 'USD',
            interval: 'month',
            features: [
              '50 AI calls/month',
              'Gemini 2.5 Flash model',
              '5 image generations/month',
              '1 monitored account',
              'Community support'
            ],
          },
          {
            id: 'reply-guy',
            name: 'Reply Guy',
            price: 9,
            currency: 'USD',
            interval: 'month',
            features: [
              '1,000 AI calls/month',
              'Gemini 2.5 Flash model',
              '20 image generations/month',
              '3 monitored accounts',
              'Email support'
            ],
          },
          {
            id: 'reply-god',
            name: 'Reply God',
            price: 29,
            currency: 'USD',
            interval: 'month',
            features: [
              '5,000 AI calls/month',
              'Gemini 2.5 Pro model',
              '100 image generations/month',
              '10 monitored accounts',
              'Priority support',
              'Custom personas',
              '3 team members'
            ],
          },
          {
            id: 'team',
            name: 'Team',
            price: 99,
            currency: 'USD',
            interval: 'month',
            features: [
              'Unlimited AI calls',
              'OpenAI o3 model',
              'Unlimited image generations',
              '50 monitored accounts',
              'Team collaboration',
              'Admin dashboard',
              'Dedicated support',
              'Custom integrations',
              '10 team members'
            ],
          },
        ];
      } catch (error) {
        console.error('Error getting available plans:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get available plans',
        });
      }
    }),

  /**
   * Get billing portal URL (for Clerk billing management)
   */
  getBillingPortalUrl: protectedProcedure
    .query(async ({ ctx }) => {
      console.log('🔍 BillingRouter: Getting billing portal URL for user:', ctx.userId);

      try {
        // In a real implementation, you would call Clerk's API to get the billing portal URL
        // For now, we'll return a placeholder
        return {
          url: `https://billing.clerk.com/portal?user_id=${ctx.userId}`,
          message: 'Billing portal integration coming soon',
        };
      } catch (error) {
        console.error('Error getting billing portal URL:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get billing portal URL',
        });
      }
    }),

  /**
   * Check if user has access to a specific plan feature
   */
  hasAccess: protectedProcedure
    .input(z.object({
      plan: z.string().optional(),
      feature: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      console.log('🔍 BillingRouter: Checking access for user:', ctx.userId, 'input:', input);

      try {
        const { has } = await auth();
        
        if (input.plan) {
          return { hasAccess: has({ plan: input.plan }) };
        }
        
        if (input.feature) {
          return { hasAccess: has({ feature: input.feature }) };
        }

        return { hasAccess: false };
      } catch (error) {
        console.error('Error checking access:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to check access',
        });
      }
    }),

  /**
   * Upgrade/downgrade user plan
   */
  changePlan: protectedProcedure
    .input(z.object({
      planId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      console.log('🔍 BillingRouter: Changing plan for user:', ctx.userId, 'to plan:', input.planId);

      try {
        // For now, this would integrate with Clerk's billing API
        // This is a placeholder implementation
        
        // Update user's plan in the database
        const updatedUser = await prisma.user.update({
          where: { id: ctx.userId },
          data: {
            // In a real implementation, you would update Clerk billing fields
            // For now, we'll just mark the intent
            lastActiveAt: new Date(),
          },
        });

        // In a real implementation, this would:
        // 1. Call Clerk's billing API to change the subscription
        // 2. Update the user's billing status
        // 3. Handle prorations and billing cycles
        // 4. Send confirmation emails

        return {
          success: true,
          message: `Plan change to ${input.planId} initiated. You will be redirected to complete the billing process.`,
          redirectUrl: `/pricing?plan=${input.planId}`,
        };
      } catch (error) {
        console.error('Error changing plan:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to change plan',
        });
      }
    }),

  /**
   * Create billing portal session
   */
  createBillingPortalSession: protectedProcedure
    .mutation(async ({ ctx }) => {
      console.log('🔍 BillingRouter: Creating billing portal session for user:', ctx.userId);

      try {
        // In a real implementation, this would call Clerk's API to create a billing portal session
        // For now, return a placeholder URL
        
        return {
          url: `/pricing?return_url=${encodeURIComponent('/profile?tab=billing')}`,
          message: 'Redirecting to billing management...',
        };
      } catch (error) {
        console.error('Error creating billing portal session:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create billing portal session',
        });
      }
    }),
});
