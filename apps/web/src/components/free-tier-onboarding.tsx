"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Zap, 
  Crown, 
  Users, 
  ArrowRight, 
  Check, 
  Gift,
  Sparkles,
  TrendingUp,
  X
} from "lucide-react"
import { trpc } from "@/utils/trpc"

interface FreeTierOnboardingProps {
  className?: string
  onDismiss?: () => void
}

export default function FreeTierOnboarding({ 
  className = "",
  onDismiss
}: FreeTierOnboardingProps) {
  const [isDismissed, setIsDismissed] = useState(false)
  const { data: subscription } = trpc.billing.getSubscription.useQuery()
  const { data: usage } = trpc.billing.getUsage.useQuery()

  // Only show for free tier users
  const currentPlan = subscription?.isClerkBilling 
    ? subscription.clerkPlan.name 
    : subscription?.legacyPlan.name || 'free'

  if (currentPlan !== 'free' || isDismissed) {
    return null
  }

  const handleDismiss = () => {
    setIsDismissed(true)
    onDismiss?.()
  }

  // Calculate overall usage across key features
  const keyFeatures = usage?.filter(u => 
    ['AI_CALLS', 'MONITORED_ACCOUNTS', 'IMAGE_GENERATIONS'].includes(u.feature)
  ) || []

  const totalUsagePercentage = keyFeatures.length > 0 
    ? keyFeatures.reduce((acc, feature) => {
        const percentage = feature.limit > 0 ? (feature.currentUsage / feature.limit) * 100 : 0
        return acc + Math.min(percentage, 100)
      }, 0) / keyFeatures.length
    : 0

  const isActiveUser = totalUsagePercentage > 20

  return (
    <Card className={`border-2 border-green-200 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 dark:border-green-800 ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-green-500 to-emerald-600 text-white">
              <Gift className="w-5 h-5" />
            </div>
            <div>
              <CardTitle className="text-green-900 dark:text-green-100 flex items-center gap-2">
                <Sparkles className="w-4 h-4" />
                Welcome to BuddyChip Free!
              </CardTitle>
              <CardDescription className="text-green-700 dark:text-green-300">
                You're all set up with our free tier. Here's what you can do:
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              <Zap className="w-3 h-3 mr-1" />
              Free Plan
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-6 w-6 p-0 text-green-600 hover:text-green-800"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Free Tier Features */}
        <div className="grid gap-3">
          <h4 className="font-semibold text-green-900 dark:text-green-100 flex items-center gap-2">
            <Check className="w-4 h-4 text-green-600" />
            What's included in your free plan:
          </h4>
          
          <div className="grid gap-2 text-sm">
            <div className="flex items-center justify-between p-2 rounded bg-white/50 dark:bg-black/20">
              <span className="text-green-800 dark:text-green-200">AI Calls per month</span>
              <span className="font-semibold text-green-900 dark:text-green-100">50</span>
            </div>
            <div className="flex items-center justify-between p-2 rounded bg-white/50 dark:bg-black/20">
              <span className="text-green-800 dark:text-green-200">Monitored accounts</span>
              <span className="font-semibold text-green-900 dark:text-green-100">1</span>
            </div>
            <div className="flex items-center justify-between p-2 rounded bg-white/50 dark:bg-black/20">
              <span className="text-green-800 dark:text-green-200">Image generations</span>
              <span className="font-semibold text-green-900 dark:text-green-100">5</span>
            </div>
            <div className="flex items-center justify-between p-2 rounded bg-white/50 dark:bg-black/20">
              <span className="text-green-800 dark:text-green-200">Community support</span>
              <span className="font-semibold text-green-900 dark:text-green-100">✓</span>
            </div>
          </div>
        </div>

        {/* Usage Progress */}
        {isActiveUser && (
          <div className="space-y-3">
            <h4 className="font-semibold text-green-900 dark:text-green-100 flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Your usage so far:
            </h4>
            
            {keyFeatures.map((feature) => {
              const percentage = feature.limit > 0 ? Math.min((feature.currentUsage / feature.limit) * 100, 100) : 0
              const featureName = feature.feature.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
              
              return (
                <div key={feature.feature} className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span className="text-green-700 dark:text-green-300">{featureName}</span>
                    <span className="text-green-800 dark:text-green-200 font-medium">
                      {feature.currentUsage} / {feature.limit}
                    </span>
                  </div>
                  <Progress value={percentage} className="h-1.5" />
                </div>
              )
            })}
          </div>
        )}

        {/* Upgrade Teaser */}
        <div className="p-4 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-semibold text-blue-900 dark:text-blue-100 flex items-center gap-2">
              <Crown className="w-4 h-4 text-blue-600" />
              Ready for more?
            </h4>
            <Badge variant="outline" className="text-blue-700 border-blue-300">
              20x More
            </Badge>
          </div>
          
          <div className="text-sm text-blue-800 dark:text-blue-200 mb-3">
            Upgrade to <strong>Reply Guy</strong> and get:
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-xs text-blue-700 dark:text-blue-300 mb-4">
            <div className="flex items-center gap-1">
              <Check className="w-3 h-3 text-blue-600" />
              1,000 AI calls/month
            </div>
            <div className="flex items-center gap-1">
              <Check className="w-3 h-3 text-blue-600" />
              3 monitored accounts
            </div>
            <div className="flex items-center gap-1">
              <Check className="w-3 h-3 text-blue-600" />
              20 image generations
            </div>
            <div className="flex items-center gap-1">
              <Check className="w-3 h-3 text-blue-600" />
              Email support
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm text-blue-700 dark:text-blue-300">
              Starting at <span className="font-bold text-blue-900 dark:text-blue-100">$9/month</span>
            </div>
            <Button 
              size="sm" 
              className="bg-blue-600 hover:bg-blue-700 text-white"
              onClick={() => window.location.href = '/pricing'}
            >
              <ArrowRight className="w-3 h-3 mr-1" />
              Upgrade
            </Button>
          </div>
        </div>

        {/* Getting Started */}
        <div className="text-center pt-2 border-t border-green-200 dark:border-green-800">
          <div className="text-sm text-green-700 dark:text-green-300 mb-2">
            New to BuddyChip? Start by adding your first monitored account!
          </div>
          <Button 
            variant="outline" 
            size="sm"
            className="border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-950/30"
            onClick={() => window.location.href = '/dashboard'}
          >
            <Sparkles className="w-3 h-3 mr-1" />
            Get Started
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
