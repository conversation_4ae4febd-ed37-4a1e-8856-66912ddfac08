"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Zap, 
  Crown, 
  Users, 
  ArrowRight, 
  Check, 
  X,
  Sparkles,
  TrendingUp
} from "lucide-react"
import { trpc } from "@/utils/trpc"
import { toast } from "sonner"

interface UpgradePromptProps {
  currentPlan: string
  feature?: string
  currentUsage?: number
  limit?: number
  className?: string
}

export default function UpgradePrompt({ 
  currentPlan, 
  feature, 
  currentUsage = 0, 
  limit = 0,
  className = "" 
}: UpgradePromptProps) {
  const [isUpgrading, setIsUpgrading] = useState(false)
  const changePlan = trpc.billing.changePlan.useMutation()

  const planHierarchy = {
    'free': {
      name: 'Free',
      icon: Zap,
      color: 'from-green-500 to-emerald-600',
      next: 'reply-guy'
    },
    'reply-guy': {
      name: '<PERSON>ly <PERSON>',
      icon: Zap,
      color: 'from-blue-500 to-cyan-600',
      next: 'reply-god'
    },
    'reply-god': {
      name: 'Reply God',
      icon: Crown,
      color: 'from-purple-500 to-pink-600',
      next: 'team'
    },
    'team': {
      name: 'Team',
      icon: Users,
      color: 'from-orange-500 to-red-600',
      next: null
    }
  }

  const planBenefits = {
    'reply-guy': [
      '1,000 AI calls/month (20x more)',
      '3 monitored accounts',
      '20 image generations/month',
      'Email support'
    ],
    'reply-god': [
      '5,000 AI calls/month',
      '10 monitored accounts',
      '100 image generations/month',
      'Priority support',
      'Custom personas',
      '3 team members'
    ],
    'team': [
      'Unlimited AI calls',
      '50 monitored accounts',
      'Unlimited image generations',
      'Team collaboration',
      'Admin dashboard',
      'Dedicated support',
      '10 team members'
    ]
  }

  const planPricing = {
    'reply-guy': 9,
    'reply-god': 29,
    'team': 99
  }

  const currentPlanInfo = planHierarchy[currentPlan as keyof typeof planHierarchy]
  const nextPlan = currentPlanInfo?.next
  const nextPlanInfo = nextPlan ? planHierarchy[nextPlan as keyof typeof planHierarchy] : null

  if (!nextPlan || currentPlan === 'team') {
    return null // Already on highest plan
  }

  const usagePercentage = limit > 0 ? Math.min((currentUsage / limit) * 100, 100) : 0
  const isNearLimit = usagePercentage >= 80
  const isAtLimit = currentUsage >= limit && limit > 0

  const handleUpgrade = async () => {
    if (!nextPlan) return

    setIsUpgrading(true)
    try {
      await changePlan.mutateAsync({ planId: nextPlan })
      toast.success(`Successfully upgraded to ${nextPlanInfo?.name}!`)
    } catch (error) {
      toast.error('Failed to upgrade plan. Please try again.')
      console.error('Upgrade error:', error)
    } finally {
      setIsUpgrading(false)
    }
  }

  const NextPlanIcon = nextPlanInfo?.icon || Zap

  return (
    <Card className={`border-2 ${isAtLimit ? 'border-red-500 bg-red-50 dark:bg-red-950/20' : isNearLimit ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20' : 'border-app-stroke'} ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg bg-gradient-to-r ${nextPlanInfo?.color} text-white`}>
              <NextPlanIcon className="w-5 h-5" />
            </div>
            <div>
              <CardTitle className="text-app-headline flex items-center gap-2">
                {isAtLimit ? (
                  <>
                    <X className="w-4 h-4 text-red-500" />
                    Limit Reached
                  </>
                ) : isNearLimit ? (
                  <>
                    <TrendingUp className="w-4 h-4 text-yellow-500" />
                    Approaching Limit
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 text-app-main" />
                    Upgrade Available
                  </>
                )}
              </CardTitle>
              <CardDescription className="text-app-sub-headline">
                {isAtLimit 
                  ? `You've reached your ${feature?.toLowerCase().replace('_', ' ')} limit`
                  : isNearLimit 
                  ? `You're using ${Math.round(usagePercentage)}% of your ${feature?.toLowerCase().replace('_', ' ')} limit`
                  : `Unlock more features with ${nextPlanInfo?.name}`
                }
              </CardDescription>
            </div>
          </div>
          <Badge variant={isAtLimit ? "destructive" : isNearLimit ? "secondary" : "default"}>
            {currentPlanInfo?.name}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Usage Progress */}
        {feature && limit > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-app-sub-headline">
                {feature.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
              </span>
              <span className="text-app-headline font-medium">
                {currentUsage} / {limit === -1 ? '∞' : limit}
              </span>
            </div>
            <Progress 
              value={usagePercentage} 
              className={`h-2 ${isAtLimit ? 'bg-red-100' : isNearLimit ? 'bg-yellow-100' : ''}`}
            />
          </div>
        )}

        {/* Upgrade Benefits */}
        <div className="space-y-3">
          <h4 className="font-semibold text-app-headline flex items-center gap-2">
            <ArrowRight className="w-4 h-4" />
            Upgrade to {nextPlanInfo?.name}
          </h4>
          
          <div className="grid gap-2">
            {planBenefits[nextPlan as keyof typeof planBenefits]?.slice(0, 4).map((benefit, index) => (
              <div key={index} className="flex items-center gap-2 text-sm text-app-sub-headline">
                <Check className="w-3 h-3 text-green-500 flex-shrink-0" />
                {benefit}
              </div>
            ))}
          </div>

          <div className="flex items-center justify-between pt-2 border-t border-app-stroke">
            <div className="text-sm text-app-sub-headline">
              Starting at
            </div>
            <div className="text-lg font-bold text-app-headline">
              ${planPricing[nextPlan as keyof typeof planPricing]}/month
            </div>
          </div>
        </div>

        {/* Upgrade Button */}
        <Button 
          onClick={handleUpgrade}
          disabled={isUpgrading}
          className="w-full bg-gradient-to-r from-app-main to-app-highlight hover:from-app-highlight hover:to-app-main text-app-secondary font-semibold"
        >
          {isUpgrading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Upgrading...
            </>
          ) : (
            <>
              <Crown className="w-4 h-4 mr-2" />
              Upgrade to {nextPlanInfo?.name}
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
}
